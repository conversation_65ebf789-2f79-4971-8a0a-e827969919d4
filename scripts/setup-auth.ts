import { exec } from 'child_process'
import { promisify } from 'util'
import { createTestUsers } from './create-test-user'

const execAsync = promisify(exec)

async function setupAuth() {
    console.log('🚀 Setting up authentication system...')
    console.log('=====================================\n')

    try {
        // Step 1: Run Prisma migration
        console.log('1️⃣ Running Prisma database migration...')
        const { stdout: migrateOutput, stderr: migrateError } = await execAsync('npx prisma db push')
        
        if (migrateError) {
            console.warn('Migration warnings:', migrateError)
        }
        console.log('✅ Database migration completed')
        console.log(migrateOutput)

        // Step 2: Generate Prisma client
        console.log('\n2️⃣ Generating Prisma client...')
        const { stdout: generateOutput } = await execAsync('npx prisma generate')
        console.log('✅ Prisma client generated')
        console.log(generateOutput)

        // Step 3: Create test users
        console.log('\n3️⃣ Creating demo test users...')
        await createTestUsers()

        console.log('\n🎉 Authentication setup completed successfully!')
        console.log('\n📝 Next steps:')
        console.log('1. Restart your development server')
        console.log('2. Try logging in with one of the demo accounts')
        console.log('3. Assign roles and departments to users in admin panel')

    } catch (error) {
        console.error('❌ Error during authentication setup:', error)
        process.exit(1)
    }
}

// Run if this file is executed directly
if (require.main === module) {
    setupAuth()
        .then(() => {
            process.exit(0)
        })
        .catch((error) => {
            console.error('Setup failed:', error)
            process.exit(1)
        })
}

export { setupAuth }
