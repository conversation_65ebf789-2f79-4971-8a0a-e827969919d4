import { createClient } from '@supabase/supabase-js'
import bcrypt from 'bcryptjs'

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing Supabase environment variables')
    console.log('Make sure you have NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY in your .env file')
    process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Demo test users data
const testUsers = [
    {
        name: 'Super Admin',
        email: '<EMAIL>',
        password: 'password123',
        nip: '************',
        phone: '081234567890'
    },
    {
        name: 'HR Admin',
        email: '<EMAIL>',
        password: 'password123',
        nip: '************',
        phone: '081234567891'
    },
    {
        name: 'P<PERSON>awa<PERSON> Demo',
        email: '<EMAIL>',
        password: 'password123',
        nip: '*********',
        phone: '081234567892'
    }
]

async function createTestUsers() {
    console.log('🚀 Creating demo test users...')
    console.log('='.repeat(40))
    
    for (const userData of testUsers) {
        try {
            console.log(`Creating user: ${userData.name} (NIP: ${userData.nip})...`)
            
            // Check if user already exists
            const { data: existingUser } = await supabase
                .from('users')
                .select('id, nip')
                .eq('nip', userData.nip)
                .single()
            
            if (existingUser) {
                console.log(`  ⚠️  User with NIP ${userData.nip} already exists, skipping...`)
                continue
            }
            
            // Hash password
            const passwordHash = await bcrypt.hash(userData.password, 12)
            
            // Create user
            const { data: user, error } = await supabase
                .from('users')
                .insert({
                    nip: userData.nip,
                    name: userData.name,
                    email: userData.email,
                    password_hash: passwordHash,
                    phone: userData.phone,
                    status: 'active'
                })
                .select('id, name, email, nip')
                .single()
            
            if (error) {
                throw new Error(error.message)
            }
            
            console.log(`  ✅ User created successfully:`, {
                id: user.id,
                name: user.name,
                email: user.email,
                nip: user.nip
            })
        } catch (error) {
            console.error(`  ❌ Error creating user ${userData.name}:`, error.message)
        }
    }
    
    console.log('\n📋 Demo Accounts Summary:')
    console.log('='.repeat(25))
    console.log('Super Admin:')
    console.log('  NIP: ************')
    console.log('  Password: password123')
    console.log('')
    console.log('HR Admin:')
    console.log('  NIP: ************')
    console.log('  Password: password123')
    console.log('')
    console.log('Pegawai:')
    console.log('  NIP: *********')
    console.log('  Password: password123')
    console.log('')
    console.log('💡 Note: Roles and departments can be assigned later in the admin panel.')
}

// Run the script
createTestUsers()
    .then(() => {
        console.log('\n✅ Demo users creation completed!')
        process.exit(0)
    })
    .catch((error) => {
        console.error('\n❌ Error in demo users creation:', error)
        process.exit(1)
    })
